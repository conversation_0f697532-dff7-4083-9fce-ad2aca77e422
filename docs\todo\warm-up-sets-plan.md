## Detailed Implementation Plan for Local Warmup Calculation

### Overview

The warmup calculation needs to be implemented client-side in the web app to replicate the MAUI app's behavior. When the API returns `WarmupsCount` but empty `WarmUpsList`, the web app will compute warmup sets locally.

### Phase 1: Type Definitions

**File: `src/types/warmup.ts`**

```typescript
// Core warmup types
export interface WarmUp {
  warmUpWeightSet: MultiUnityWeight
  warmUpReps: number
}

export interface MultiUnityWeight {
  kg: number
  lb: number
}

// Warmup calculation configuration
export interface WarmupCalculationConfig {
  warmupsCount: number
  workingWeight: MultiUnityWeight
  workingReps: number
  incrementValue: number
  minWeight?: number
  maxWeight?: number
  isPlateAvailable: boolean
  isBodyweight: boolean
  barbellWeight: number
  availablePlates: string
  userBodyWeight: number
  isKg: boolean
}

// Plate calculation types
export interface PlateSet {
  plateWeight: number
  count: number
}

export interface PlateCalculationResult {
  totalWeight: number
  platesPerSide: PlateSet[]
}
```

### Phase 2: Core Warmup Algorithm

**File: `src/utils/warmupCalculator.ts`**

```typescript
import {
  WarmUp,
  MultiUnityWeight,
  WarmupCalculationConfig,
} from '@/types/warmup'

export class WarmupCalculator {
  private static readonly KG_TO_LB = 2.20462
  private static readonly INITIAL_WEIGHT_PERCENTAGE = 0.5
  private static readonly FINAL_WEIGHT_PERCENTAGE = 0.85
  private static readonly BODYWEIGHT_INITIAL_REPS_PERCENTAGE = 0.6
  private static readonly WEIGHTED_INITIAL_REPS_PERCENTAGE = 0.75
  private static readonly WEIGHTED_FINAL_REPS_PERCENTAGE = 0.4
  private static readonly MIN_REPS_THRESHOLD = 5.01
  private static readonly MIN_REPS_DEFAULT = 6
  private static readonly MIN_REPS_WEIGHTED = 3

  static computeWarmups(config: WarmupCalculationConfig): WarmUp[] {
    // No warmups if count is 0
    if (config.warmupsCount === 0) {
      return []
    }

    const warmups: WarmUp[] = []
    const warmupsCount = config.warmupsCount
    const newWarmupCount = warmupsCount > 1 ? warmupsCount - 1 : warmupsCount

    // Calculate weight progression
    const weightProgression = this.calculateWeightProgression(
      config.workingWeight.kg,
      config.warmupsCount,
      newWarmupCount
    )

    // Calculate reps progression
    const repsProgression = this.calculateRepsProgression(
      config.workingReps,
      config.warmupsCount,
      newWarmupCount,
      config.isBodyweight,
      config.isPlateAvailable
    )

    // Generate warmup sets
    for (let i = 0; i < warmupsCount; i++) {
      let warmupWeight: number
      let warmupReps: number

      if (config.isBodyweight) {
        // Bodyweight exercises use working weight with varying reps
        warmupWeight = config.workingWeight.kg
        warmupReps = Math.ceil(
          repsProgression.initial -
            repsProgression.decrement * (warmupsCount - (i + 1))
        )
      } else {
        // Weighted exercises progress weight from 50% to 85%
        warmupWeight =
          weightProgression.initial + weightProgression.increment * i
        warmupReps = Math.ceil(
          repsProgression.initial - repsProgression.decrement * i
        )

        // Apply minimum reps for weighted exercises
        if (warmupReps < this.MIN_REPS_WEIGHTED && !config.isPlateAvailable) {
          warmupReps = this.MIN_REPS_WEIGHTED
        }
      }

      // Round to available increments
      warmupWeight = this.roundToNearestIncrement(
        warmupWeight,
        config.incrementValue,
        config.minWeight,
        config.maxWeight
      )

      // Apply plate calculations for barbell exercises
      if (config.isPlateAvailable && !config.isBodyweight) {
        warmupWeight = this.calculatePlatesWeight(
          config.availablePlates,
          warmupWeight,
          config.barbellWeight,
          config.isKg
        )
      }

      warmups.push({
        warmUpWeightSet: {
          kg: warmupWeight,
          lb: warmupWeight * this.KG_TO_LB,
        },
        warmUpReps: warmupReps,
      })
    }

    // Special case: First warmup for weighted exercises is bodyweight only
    if (!config.isBodyweight && warmups.length > 0) {
      warmups[0].warmUpWeightSet = { kg: 0, lb: 0 }
    }

    return warmups
  }

  private static calculateWeightProgression(
    workingWeightKg: number,
    warmupsCount: number,
    newWarmupCount: number
  ) {
    const initialWeight = workingWeightKg * this.INITIAL_WEIGHT_PERCENTAGE
    const finalWeight = workingWeightKg * this.FINAL_WEIGHT_PERCENTAGE
    const weightIncrement = (finalWeight - initialWeight) / newWarmupCount

    return {
      initial: initialWeight,
      increment: weightIncrement,
    }
  }

  private static calculateRepsProgression(
    workingReps: number,
    warmupsCount: number,
    newWarmupCount: number,
    isBodyweight: boolean,
    isPlateAvailable: boolean
  ) {
    let initialReps: number
    let repsDecrement: number

    if (isBodyweight) {
      // Bodyweight: 60% of working reps
      initialReps = workingReps * this.BODYWEIGHT_INITIAL_REPS_PERCENTAGE
      repsDecrement =
        warmupsCount === 1
          ? workingReps * this.BODYWEIGHT_INITIAL_REPS_PERCENTAGE
          : (workingReps * 0.6 - workingReps * 0.5) / newWarmupCount
    } else {
      // Weighted: 75% to 40% of working reps
      initialReps = workingReps * this.WEIGHTED_INITIAL_REPS_PERCENTAGE
      const finalReps = workingReps * this.WEIGHTED_FINAL_REPS_PERCENTAGE
      repsDecrement = (initialReps - finalReps) / newWarmupCount

      // Minimum reps adjustment for low rep ranges
      if (initialReps < this.MIN_REPS_THRESHOLD && !isPlateAvailable) {
        initialReps = this.MIN_REPS_DEFAULT
      }
    }

    return {
      initial: initialReps,
      decrement: repsDecrement,
    }
  }

  private static roundToNearestIncrement(
    weight: number,
    increment: number,
    min?: number,
    max?: number
  ): number {
    const rounded = Math.round(weight / increment) * increment

    if (min && rounded < min) return min
    if (max && rounded > max) return max

    return rounded
  }

  private static calculatePlatesWeight(
    availablePlates: string,
    targetWeight: number,
    barbellWeight: number,
    isKg: boolean
  ): number {
    // Return barbell weight if target is less than barbell
    if (targetWeight <= barbellWeight) {
      return barbellWeight
    }

    // Calculate weight needed per side
    const weightPerSide = (targetWeight - barbellWeight) / 2

    // Available plate weights (sorted descending)
    const plates = isKg
      ? [20, 15, 10, 5, 2.5, 1.25, 0.5, 0.25]
      : [45, 35, 25, 10, 5, 2.5]

    // Parse available plates from string (if needed)
    // For now, use all plates. Could be enhanced to parse from availablePlates string

    let remainingWeight = weightPerSide
    let totalPlateWeight = 0

    // Greedy algorithm to select plates
    for (const plate of plates) {
      while (remainingWeight >= plate) {
        totalPlateWeight += plate
        remainingWeight -= plate
      }
    }

    // Return barbell + plates on both sides
    return barbellWeight + totalPlateWeight * 2
  }
}
```

### Phase 3: Exercise Type Detection

**File: `src/utils/exerciseHelpers.ts`**

```typescript
export interface ExerciseInfo {
  isBodyweight: boolean
  isWeighted: boolean
  usesPlates: boolean
  exerciseType: 'bodyweight' | 'barbell' | 'dumbbell' | 'machine' | 'cable'
}

export class ExerciseHelpers {
  // Common bodyweight exercise patterns
  private static readonly BODYWEIGHT_PATTERNS = [
    'push-up',
    'push up',
    'pushup',
    'pull-up',
    'pull up',
    'pullup',
    'chin-up',
    'chin up',
    'chinup',
    'dip',
    'plank',
    'burpee',
    'squat (bodyweight)',
    'bodyweight squat',
    'lunge (bodyweight)',
    'bodyweight lunge',
    'crunch',
    'sit-up',
    'sit up',
  ]

  // Barbell exercise patterns
  private static readonly BARBELL_PATTERNS = [
    'barbell',
    'bench press',
    'squat',
    'deadlift',
    'overhead press',
    'row',
    'curl',
    'military press',
  ]

  static getExerciseInfo(exerciseName: string): ExerciseInfo {
    const nameLower = exerciseName.toLowerCase()

    // Check if bodyweight
    const isBodyweight = this.BODYWEIGHT_PATTERNS.some((pattern) =>
      nameLower.includes(pattern)
    )

    // Check if uses barbell (plates)
    const usesPlates = this.BARBELL_PATTERNS.some((pattern) =>
      nameLower.includes(pattern)
    )

    // Determine exercise type
    let exerciseType: ExerciseInfo['exerciseType'] = 'machine'
    if (isBodyweight) {
      exerciseType = 'bodyweight'
    } else if (usesPlates) {
      exerciseType = 'barbell'
    } else if (nameLower.includes('dumbbell') || nameLower.includes('db')) {
      exerciseType = 'dumbbell'
    } else if (nameLower.includes('cable')) {
      exerciseType = 'cable'
    }

    return {
      isBodyweight,
      isWeighted: !isBodyweight,
      usesPlates,
      exerciseType,
    }
  }

  static getBarbellWeight(exerciseName: string, userSettings: any): number {
    const nameLower = exerciseName.toLowerCase()

    // Default barbell weights
    const defaultBarbellKg = 20
    const defaultBarbellLb = 45

    // Check user settings first
    if (userSettings?.barbellWeight) {
      return userSettings.barbellWeight
    }

    // Special cases for different barbells
    if (nameLower.includes('ez bar') || nameLower.includes('curl bar')) {
      return userSettings?.unit === 'kg' ? 7 : 15
    }

    if (nameLower.includes('trap bar') || nameLower.includes('hex bar')) {
      return userSettings?.unit === 'kg' ? 25 : 55
    }

    // Standard barbell
    return userSettings?.unit === 'kg' ? defaultBarbellKg : defaultBarbellLb
  }
}
```

### Phase 4: Integration with Recommendation Service

**File: `src/services/recommendationService.ts`**

```typescript
import { WarmupCalculator } from '@/utils/warmupCalculator'
import { ExerciseHelpers } from '@/utils/exerciseHelpers'
import { RecommendationModel } from '@/types/api'
import { WarmUp } from '@/types/warmup'

export class RecommendationService {
  static async getRecommendationWithWarmups(
    exerciseId: number,
    userSettings: any
  ): Promise<RecommendationModel> {
    // 1. Get recommendation from API
    const recommendation = await this.fetchRecommendation(exerciseId)

    // 2. Check if warmups need to be calculated locally
    if (
      recommendation.WarmupsCount > 0 &&
      (!recommendation.WarmUpsList || recommendation.WarmUpsList.length === 0)
    ) {
      // 3. Get exercise information
      const exerciseInfo = ExerciseHelpers.getExerciseInfo(
        recommendation.ExerciseName
      )

      // 4. Calculate warmups
      const warmups = WarmupCalculator.computeWarmups({
        warmupsCount: recommendation.WarmupsCount,
        workingWeight: recommendation.Weight,
        workingReps: recommendation.Reps,
        incrementValue: recommendation.Increments?.kg || 1,
        minWeight: recommendation.Min?.kg,
        maxWeight: recommendation.Max?.kg,
        isPlateAvailable: recommendation.isPlateAvailable,
        isBodyweight: exerciseInfo.isBodyweight,
        barbellWeight: ExerciseHelpers.getBarbellWeight(
          recommendation.ExerciseName,
          userSettings
        ),
        availablePlates: userSettings.availablePlates || '',
        userBodyWeight: userSettings.bodyWeight || 0,
        isKg: userSettings.unit === 'kg',
      })

      // 5. Merge warmups into recommendation
      recommendation.WarmUpsList = warmups
    }

    return recommendation
  }

  private static async fetchRecommendation(
    exerciseId: number
  ): Promise<RecommendationModel> {
    // Implementation depends on your API client
    // Example:
    const response = await fetch(
      `/api/recommendations/${exerciseId}/without-warmups`,
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      }
    )

    if (!response.ok) {
      throw new Error('Failed to fetch recommendation')
    }

    return response.json()
  }
}
```

### Phase 5: UI Component for Displaying Sets

**File: `src/components/workout/SetsList.tsx`**

```typescript
import React from 'react';
import { RecommendationModel } from '@/types/api';
import { WarmUp } from '@/types/warmup';

interface SetsListProps {
  recommendation: RecommendationModel;
  onSetComplete: (setIndex: number, isWarmup: boolean) => void;
}

export const SetsList: React.FC<SetsListProps> = ({
  recommendation,
  onSetComplete
}) => {
  const warmups = recommendation.WarmUpsList || [];
  const workingSets = recommendation.Series || [];

  return (
    <div className="sets-list">
      {/* Warmup Sets */}
      {warmups.length > 0 && (
        <div className="warmup-section">
          <h3 className="text-sm font-semibold text-gray-600 mb-2">
            Warm-up Sets
          </h3>
          {warmups.map((warmup: WarmUp, index: number) => (
            <SetRow
              key={`warmup-${index}`}
              setNumber={index + 1}
              weight={warmup.warmUpWeightSet}
              reps={warmup.warmUpReps}
              isWarmup={true}
              onComplete={() => onSetComplete(index, true)}
            />
          ))}
        </div>
      )}

      {/* Working Sets */}
      <div className="working-sets-section mt-4">
        <h3 className="text-sm font-semibold text-gray-800 mb-2">
          Working Sets
        </h3>
        {workingSets.map((set: any, index: number) => (
          <SetRow
            key={`working-${index}`}
            setNumber={warmups.length + index + 1}
            weight={recommendation.Weight}
            reps={set.Reps}
            isWarmup={false}
            onComplete={() => onSetComplete(index, false)}
          />
        ))}
      </div>
    </div>
  );
};

interface SetRowProps {
  setNumber: number;
  weight: { kg: number; lb: number };
  reps: number;
  isWarmup: boolean;
  onComplete: () => void;
}

const SetRow: React.FC<SetRowProps> = ({
  setNumber,
  weight,
  reps,
  isWarmup,
  onComplete
}) => {
  const unit = useUserUnit(); // Custom hook to get user's preferred unit
  const displayWeight = unit === 'kg' ? weight.kg : weight.lb;

  return (
    <div className={`set-row flex items-center justify-between p-3 mb-2
      ${isWarmup ? 'bg-blue-50' : 'bg-gray-50'} rounded-lg`}>
      <div className="set-info flex items-center gap-4">
        <span className="set-number text-sm font-medium">
          Set {setNumber}
        </span>
        <span className="weight-reps text-lg font-semibold">
          {displayWeight > 0 ? `${displayWeight} ${unit}` : 'Bodyweight'} × {reps}
        </span>
        {isWarmup && (
          <span className="warmup-badge text-xs bg-blue-100 text-blue-700
            px-2 py-1 rounded">
            Warm-up
          </span>
        )}
      </div>
      <button
        onClick={onComplete}
        className="complete-btn px-4 py-2 bg-green-500 text-white
          rounded-md hover:bg-green-600 transition-colors"
      >
        Complete
      </button>
    </div>
  );
};
```

### Phase 6: Unit Tests

**File: `src/utils/__tests__/warmupCalculator.test.ts`**

```typescript
import { WarmupCalculator } from '../warmupCalculator'
import { WarmupCalculationConfig } from '@/types/warmup'

describe('WarmupCalculator', () => {
  describe('Weighted Exercise Warmups', () => {
    it('should calculate 3 warmup sets for bench press', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 3,
        workingWeight: { kg: 100, lb: 220 },
        workingReps: 10,
        incrementValue: 2.5,
        isPlateAvailable: true,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: 'all',
        userBodyWeight: 80,
        isKg: true,
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      expect(warmups).toHaveLength(3)

      // First warmup should be bodyweight only
      expect(warmups[0].warmUpWeightSet.kg).toBe(0)

      // Second warmup ~50% of working weight
      expect(warmups[1].warmUpWeightSet.kg).toBeCloseTo(50, 0)

      // Third warmup ~85% of working weight
      expect(warmups[2].warmUpWeightSet.kg).toBeCloseTo(85, 0)

      // Reps should decrease
      expect(warmups[0].warmUpReps).toBeGreaterThan(warmups[2].warmUpReps)
    })
  })

  describe('Bodyweight Exercise Warmups', () => {
    it('should calculate warmups for push-ups', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 2,
        workingWeight: { kg: 80, lb: 176 }, // User bodyweight
        workingReps: 20,
        incrementValue: 1,
        isPlateAvailable: false,
        isBodyweight: true,
        barbellWeight: 0,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true,
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      expect(warmups).toHaveLength(2)

      // Weight should remain constant (bodyweight)
      expect(warmups[0].warmUpWeightSet.kg).toBe(80)
      expect(warmups[1].warmUpWeightSet.kg).toBe(80)

      // Reps should be ~60% of working reps
      expect(warmups[0].warmUpReps).toBeCloseTo(12, 0)
    })
  })

  describe('Plate Calculations', () => {
    it('should round to available plates', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 1,
        workingWeight: { kg: 67.5, lb: 148.5 },
        workingReps: 8,
        incrementValue: 2.5,
        isPlateAvailable: true,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: 'all',
        userBodyWeight: 75,
        isKg: true,
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      // Should round to nearest plate combination
      const expectedWeight = 20 + 2 * 15 // Barbell + 15kg each side
      expect(warmups[0].warmUpWeightSet.kg).toBe(50)
    })
  })
})
```

### Phase 7: Integration Example

**File: `src/pages/workout/ExercisePage.tsx`**

```typescript
import { useEffect, useState } from 'react';
import { RecommendationService } from '@/services/recommendationService';
import { SetsList } from '@/components/workout/SetsList';
import { useUserSettings } from '@/hooks/useUserSettings';

export const ExercisePage = ({ exerciseId }: { exerciseId: number }) => {
  const [recommendation, setRecommendation] = useState(null);
  const [loading, setLoading] = useState(true);
  const userSettings = useUserSettings();

  useEffect(() => {
    loadRecommendation();
  }, [exerciseId]);

  const loadRecommendation = async () => {
    try {
      setLoading(true);
      const reco = await RecommendationService.getRecommendationWithWarmups(
        exerciseId,
        userSettings
      );
      setRecommendation(reco);
    } catch (error) {
      console.error('Failed to load recommendation:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSetComplete = (setIndex: number, isWarmup: boolean) => {
    // Handle set completion logic
    console.log(`Completed ${isWarmup ? 'warmup' : 'working'} set ${setIndex}`);
  };

  if (loading) return <LoadingSpinner />;
  if (!recommendation) return <ErrorMessage />;

  return (
    <div className="exercise-page">
      <h1>{recommendation.ExerciseName}</h1>
      <SetsList
        recommendation={recommendation}
        onSetComplete={handleSetComplete}
      />
    </div>
  );
};
```

### Key Implementation Notes:

1. **Algorithm Accuracy**: The warmup calculation follows the exact percentages from the MAUI app (50-85% for weight, 75-40% for reps)

2. **Special Cases Handled**:
   - Bodyweight exercises maintain constant weight, vary reps
   - Minimum rep thresholds for safety
   - Plate rounding for realistic weights

3. **User Settings Integration**:
   - Unit preference (kg/lb)
   - Available plates
   - Barbell weight
   - Body weight

4. **Testing Strategy**:
   - Unit tests for calculation logic
   - Integration tests for API flow
   - UI tests for display components

5. **Performance**: Calculations are lightweight and run instantly on the client

This implementation provides a complete solution for local warmup calculation that matches the MAUI app's behavior.
